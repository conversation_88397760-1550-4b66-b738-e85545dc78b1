import React, { useState, useCallback, useEffect } from 'react'
import { Table, Button, Space, Typography, Pagination, Alert, Tag } from 'antd'
import { EyeOutlined, HistoryOutlined, DownOutlined, RightOutlined } from '@ant-design/icons'
import { message } from 'antd'
import { materialSmartDescriptionApi } from '../../../services/api'
import { getStateTag } from '@/constants/common'
import { formatTime, renderZhidaDescription } from './shared/utils'
import { DEFAULT_PAGE_SIZE } from './shared/constants'
import type {
  MaterialSmartDescription,
  MaterialGroupedDescription,
  PaginationParams,
  DescriptionListSectionProps,
} from './shared/types'
import styles from './MaterialSmartDescriptionList.module.less'

const { Text } = Typography

const DescriptionListSection: React.FC<DescriptionListSectionProps> = ({
  onViewDetail,
  onViewHistory,
}) => {
  // 列表数据状态
  const [data, setData] = useState<MaterialGroupedDescription[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([])

  // 获取列表数据
  const fetchData = useCallback(async (params?: Partial<PaginationParams>) => {
    setLoading(true)
    try {
      const response = await materialSmartDescriptionApi.getDescriptions({
        pageNum: currentPage,
        pageSize,
        ...params,
      })
      if (response.data.code === 1) {
        setData(response.data.data.list)
        setTotal(response.data.data.total)
      }
      else {
        message.error(response.data.message || '获取数据失败')
      }
    }
    catch (error) {
      console.error('获取数据失败:', error)
      message.error('获取数据失败，请稍后重试')
    }
    finally {
      setLoading(false)
    }
  }, [currentPage, pageSize])

  // 分页变化处理
  const handlePageChange = useCallback((page: number, size?: number) => {
    if (size && size !== pageSize) {
      setPageSize(size)
      setCurrentPage(1)
    }
    else {
      setCurrentPage(page)
    }
  }, [pageSize])

  // 初始化数据
  useEffect(() => {
    fetchData({ pageNum: currentPage, pageSize })
  }, [currentPage, pageSize, fetchData])

  // 展开/收起处理
  const handleExpand = (expanded: boolean, record: MaterialGroupedDescription) => {
    const key = record.materialId
    if (expanded) {
      setExpandedRowKeys([...expandedRowKeys, key])
    } else {
      setExpandedRowKeys(expandedRowKeys.filter(k => k !== key))
    }
  }

  // 子表格列定义（版本详情）
  const versionColumns = [
    {
      title: '版本',
      dataIndex: 'materialVersion',
      key: 'materialVersion',
      width: 120,
      render: (version: string) => (
        <Tag color="blue" style={{ fontSize: '12px' }}>
          {version}
        </Tag>
      ),
    },
    {
      title: 'ZhiDa 描述',
      dataIndex: 'zhidaDescription',
      key: 'zhidaDescription',
      width: 400,
      render: (_: unknown, record: MaterialSmartDescription) =>
        renderZhidaDescription(record.zhidaDescription),
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 180,
      render: (record: MaterialSmartDescription) => (
        <Text style={{ fontSize: '13px', color: '#595959' }}>
          {formatTime(record.createTime)}
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: getStateTag,
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (record: MaterialSmartDescription) => (
        <Space size={4} direction="vertical">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => onViewDetail(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#1890ff',
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => onViewHistory(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#722ed1',
            }}
          >
            历史
          </Button>
        </Space>
      ),
    },
  ]

  // 主表格列定义（物料信息）
  const columns = [
    {
      title: '物料信息',
      key: 'material',
      width: 400,
      render: (record: MaterialGroupedDescription) => (
        <div>
          <div style={{ marginBottom: 8 }}>
            <Text strong style={{ fontSize: '14px', color: '#262626' }}>
              {record.materialDetail.title}
            </Text>
          </div>
          <div style={{ marginBottom: 6 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>组件名称：</Text>
            <Text code style={{ fontSize: '12px', background: '#e6f7ff', color: '#52c41a', padding: '2px 6px', borderRadius: '4px', fontWeight: 500 }}>
              {record.materialDetail.name}
            </Text>
          </div>
          <div style={{ marginBottom: 6 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>命名空间：</Text>
            <Text code style={{ fontSize: '12px', background: '#f6f8fa', padding: '2px 6px', borderRadius: '4px' }}>
              {record.materialDetail.namespace}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '版本统计',
      key: 'versions',
      width: 200,
      render: (record: MaterialGroupedDescription) => (
        <div>
          <div style={{ marginBottom: 6 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>总版本数：</Text>
            <Tag color="green" style={{ fontSize: '12px' }}>
              {record.totalVersions}
            </Tag>
          </div>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>最新版本：</Text>
            <Tag color="blue" style={{ fontSize: '12px' }}>
              {record.latestVersion}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: '最新描述',
      key: 'latestDescription',
      width: 400,
      render: (record: MaterialGroupedDescription) => {
        const latestVersion = record.versions[0] // 已按时间排序，第一个是最新的
        return latestVersion ? renderZhidaDescription(latestVersion.zhidaDescription) : '-'
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (record: MaterialGroupedDescription) => {
        const latestVersion = record.versions[0]
        return latestVersion ? (
          <Space size={4} direction="vertical">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewDetail(latestVersion)}
              style={{
                padding: '2px 8px',
                height: 'auto',
                fontSize: '12px',
                color: '#1890ff',
              }}
            >
              详情
            </Button>
            <Button
              type="link"
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => onViewHistory(latestVersion)}
              style={{
                padding: '2px 8px',
                height: 'auto',
                fontSize: '12px',
                color: '#722ed1',
              }}
            >
              历史
            </Button>
          </Space>
        ) : null
      },
    },
  ]

  // 展开行渲染
  const expandedRowRender = (record: MaterialGroupedDescription) => {
    return (
      <Table
        columns={versionColumns}
        dataSource={record.versions}
        rowKey="id"
        pagination={false}
        size="small"
        style={{ margin: '0 48px' }}
      />
    )
  }

  return (
    <div>
      <div className={styles.searchSection}>
        <Alert
          message="数据说明"
          description="列表按物料分组显示，每行代表一个物料及其所有版本。点击展开按钮可查看该物料的所有版本详情。"
          type="info"
          showIcon
          closable={false}
        />
      </div>

      <div className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="materialId"
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
          expandable={{
            expandedRowRender,
            onExpand: handleExpand,
            expandedRowKeys,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Button
                type="text"
                size="small"
                icon={expanded ? <DownOutlined /> : <RightOutlined />}
                onClick={(e) => onExpand(record, e)}
                style={{ padding: '0 4px' }}
              />
            ),
          }}
        />

        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条物料`}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>
    </div>
  )
}

export default DescriptionListSection
