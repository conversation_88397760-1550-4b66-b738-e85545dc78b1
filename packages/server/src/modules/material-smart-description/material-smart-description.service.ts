import { Injectable, Logger } from '@nestjs/common'
import { get } from 'lodash'

import { MaterialSmartDescription } from '../../database/models'
import { ForwardMaterialPlatformService } from '../forward/material-platform/material-platform.service'

// 导入子服务
import { MaterialSmartDescriptionQueryService } from './services/query.service'
import { MaterialSmartDescriptionCreationService } from './services/creation.service'
import { MaterialSmartDescriptionValidationService } from './services/validation.service'
import { MaterialSmartDescriptionFallbackService } from './services/fallback.service'

// 物料 Schema 接口定义
interface MaterialSchema {
  componentName: string
  componentChineseName?: string
  [key: string]: unknown
}

/**
 * 物料智能描述主服务
 * 重构后专注于协调各个子服务，遵循单一职责原则和 KISS 原则
 */
@Injectable()
export class MaterialSmartDescriptionService {
  private readonly logger = new Logger(MaterialSmartDescriptionService.name)

  constructor(
    private readonly queryService: MaterialSmartDescriptionQueryService,
    private readonly creationService: MaterialSmartDescriptionCreationService,
    private readonly validationService: MaterialSmartDescriptionValidationService,
    private readonly fallbackService: MaterialSmartDescriptionFallbackService,
    private readonly materialPlatformService: ForwardMaterialPlatformService,
  ) {}

  // ==================== 创建相关方法（委托给创建服务） ====================

  /**
   * 创建智能描述记录
   */
  async createSmartDescription(params: {
    jobId: number
    materialId: number
    materialPubId: number
    materialVersion: string
    result: Service.MaterialSmartDescription.BasicMaterialDescription
    namespace: string
    schemaUrl: string
  }): Promise<MaterialSmartDescription> {
    return this.creationService.createSmartDescription(params)
  }

  /**
   * 基于现有记录创建新记录
   */
  async createSmartDescriptionFromExisting(
    params: Service.MaterialSmartDescription.CreateSmartDescriptionFromExistingParams,
  ): Promise<MaterialSmartDescription | null> {
    return this.creationService.createSmartDescriptionFromExisting(params)
  }

  /**
   * 更新 ZhiDa 描述
   */
  async updateZhidaDescription(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    return this.creationService.updateZhidaDescription(params)
  }

  // ==================== 查询相关方法（委托给查询服务） ====================

  /**
   * 根据 ID 查找描述
   */
  async findDescriptionById(
    id: number,
  ): Promise<MaterialSmartDescription | null> {
    return this.queryService.findDescriptionById(id)
  }

  /**
   * 根据物料 ID 查找描述
   */
  async findDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    return this.queryService.findDescriptionsByMaterialId(
      materialId,
      limit,
      offset,
    )
  }

  /**
   * 根据物料标识参数查找最新的有效描述
   */
  async findLatestDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    return this.queryService.findLatestDescriptionByMaterialIdentifier(params)
  }

  /**
   * 根据命名空间查找描述
   */
  async findDescriptionsByNamespace(
    namespace: string,
    limit: number,
    offset: number,
  ): Promise<MaterialSmartDescription[]> {
    return this.queryService.findDescriptionsByNamespace(
      namespace,
      limit,
      offset,
    )
  }

  /**
   * 根据命名空间统计描述数量
   */
  async countDescriptionsByNamespace(namespace: string): Promise<number> {
    return this.queryService.countDescriptionsByNamespace(namespace)
  }

  /**
   * 根据发布状态查找描述
   */
  async findDescriptionsByPublishStatus(
    publishStatus: number,
    limit: number,
    offset: number,
  ): Promise<MaterialSmartDescription[]> {
    return this.queryService.findDescriptionsByPublishStatus(
      publishStatus,
      limit,
      offset,
    )
  }

  /**
   * 根据发布状态统计描述数量
   */
  async countDescriptionsByPublishStatus(
    publishStatus: number,
  ): Promise<number> {
    return this.queryService.countDescriptionsByPublishStatus(publishStatus)
  }

  /**
   * 更新发布状态
   */
  async updatePublishStatus(
    id: number,
    publishStatus: number,
  ): Promise<{ success: boolean, message: string }> {
    return this.creationService.updatePublishStatus(id, publishStatus)
  }

  /**
   * 分页获取按物料分组的智能描述列表（替换原有的 getEnrichedDescriptions）
   */
  async getEnrichedDescriptions(
    params: Service.PaginationParams,
  ): Promise<Service.MaterialSmartDescription.MaterialGroupedDescriptionList> {
    return this.getGroupedDescriptions(params)
  }

  /**
   * 根据 ID 获取富化的描述详情
   */
  async getEnrichedDescriptionById(
    id: number,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription | null> {
    const description = await this.queryService.findDescriptionById(id)
    if (!description) {
      return null
    }

    const materialDetail = await this.getMaterialDetailInfoFromDescription(
      description,
    )
    const zhidaDescription = this.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })

    return {
      ...description,
      materialDetail,
      zhidaDescription,
    }
  }

  /**
   * 分页获取按物料分组的智能描述列表
   */
  async getGroupedDescriptions(
    params: Service.PaginationParams,
  ): Promise<Service.MaterialSmartDescription.MaterialGroupedDescriptionList> {
    // 获取分页的物料 ID 列表
    const groupedResult = await this.queryService.getGroupedDescriptionsRaw(params)

    if (groupedResult.materialIds.length === 0) {
      return {
        list: [],
        total: groupedResult.total,
        pageNum: groupedResult.pageNum,
        pageSize: groupedResult.pageSize,
        totalPage: groupedResult.totalPage,
        hasNext: groupedResult.hasNext,
        hasPrev: groupedResult.hasPrev,
      }
    }

    // 获取这些物料的所有版本描述
    const descriptionsGrouped = await this.queryService.getDescriptionsByMaterialIds(
      groupedResult.materialIds,
    )

    // 构建分组结果
    const groupedDescriptions = await Promise.all(
      groupedResult.materialIds.map(async (materialId) => {
        const rawDescriptions = descriptionsGrouped[materialId] || []

        if (rawDescriptions.length === 0) {
          return null
        }

        // 富化所有版本的描述
        const enrichedVersions = await Promise.all(
          rawDescriptions.map(async raw => this.enrichRawDescription(raw)),
        )

        // 获取物料基础信息（使用第一个版本的信息）
        const firstVersion = enrichedVersions[0]
        const materialDetail = firstVersion.materialDetail

        // 计算版本信息
        const versions = enrichedVersions.sort((a, b) => b.createTime - a.createTime)
        const latestVersion = versions[0].materialVersion

        return {
          materialId,
          materialDetail,
          totalVersions: versions.length,
          latestVersion,
          versions,
        } as Service.MaterialSmartDescription.MaterialGroupedDescription
      }),
    )

    // 过滤掉 null 值
    const validGroupedDescriptions = groupedDescriptions.filter(
      (item): item is Service.MaterialSmartDescription.MaterialGroupedDescription => item !== null,
    )

    return {
      list: validGroupedDescriptions,
      total: groupedResult.total,
      pageNum: groupedResult.pageNum,
      pageSize: groupedResult.pageSize,
      totalPage: groupedResult.totalPage,
      hasNext: groupedResult.hasNext,
      hasPrev: groupedResult.hasPrev,
    }
  }

  // ==================== 业务逻辑方法（主要接口） ====================

  /**
   * 通过物料标识参数查询物料的 zhidaDescription
   */
  async getDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription | null> {
    // 验证参数
    ForwardMaterialPlatformService.assertMaterialIdentifier(params)

    // 查找最新的描述记录
    const description
      = await this.queryService.findLatestDescriptionByMaterialIdentifier(params)

    if (!description) {
      return null
    }

    // 获取物料基础信息并构建 zhidaDescription
    const materialDetail = await this.getMaterialDetailInfoFromDescription(
      description,
    )
    return this.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })
  }

  /**
   * 通过物料标识参数查询物料的 zhidaDescription，支持版本回退
   */
  async getDescriptionByMaterialIdentifierWithFallback(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescriptionWithVersion | null> {
    // 验证参数
    ForwardMaterialPlatformService.assertMaterialIdentifier(params)

    return this.fallbackService.getDescriptionWithFallback(
      params,
      this.buildZhidaDescription.bind(this),
      this.getMaterialDetailInfoFromDescription.bind(this),
    )
  }

  /**
   * 更新 ZhiDa 描述并返回富化的结果
   */
  async updateZhidaDescriptionWithEnrichment(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    const result = await this.creationService.updateZhidaDescription(params)

    if (!result.success || !result.data) {
      return result
    }

    // 获取富化的结果
    const enrichedData = await this.getEnrichedDescriptionById(result.data.id)

    return {
      ...result,
      data: enrichedData || result.data,
    }
  }

  // ==================== 核心辅助方法 ====================

  /**
   * 构建 ZhiDa 描述对象
   */
  buildZhidaDescription(
    params: Service.MaterialSmartDescription.BuildZhidaDescriptionParams,
  ): Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription {
    const { smartDescription, materialDetail } = params

    return {
      title: materialDetail.title,
      name: materialDetail.name,
      namespace: materialDetail.namespace,
      description: get(
        smartDescription,
        'exportType.explanation',
        '',
      ) as string,
      propsDefine: get(
        smartDescription,
        'api.parameters.typescriptCode',
        '',
      ) as string,
      jsxDemo: get(smartDescription, 'usage.basicExample', []) as string[],
      ...(get(
        smartDescription,
        'payload',
        {},
      ) as Partial<Service.MaterialSmartDescription.MaterialUserDefine>),
    }
  }

  /**
   * 从 CDN 获取物料 Schema 数据
   */
  async getSchemaFromCDN(schemaUrl: string): Promise<MaterialSchema | null> {
    return this.validationService.getAndValidateSchemaFromCDN(schemaUrl)
  }

  /**
   * 从 Schema 数据中提取物料基础信息
   */
  extractMaterialDetailFromSchema(
    schema: MaterialSchema,
    namespace: string,
  ): Service.MaterialSmartDescription.MaterialDetailInfo {
    return {
      title: schema.componentChineseName || schema.componentName || '未知物料',
      name: schema.componentName || 'unknown',
      namespace: namespace || '未知',
    }
  }

  /**
   * 从描述记录获取物料基础信息（优先从 schema 获取）
   */
  async getMaterialDetailInfoFromDescription(
    description: MaterialSmartDescription,
  ): Promise<Service.MaterialSmartDescription.MaterialDetailInfo> {
    // 优先从 schemaUrl 获取
    if (description.schemaUrl) {
      try {
        const schema = await this.getSchemaFromCDN(description.schemaUrl)
        if (schema) {
          return this.extractMaterialDetailFromSchema(
            schema,
            description.namespace,
          )
        }
      }
      catch (error) {
        this.logger.warn(
          `从 schema 获取物料信息失败，fallback 到原方法: ${
            error instanceof Error ? error.message : '未知错误'
          }`,
        )
      }
    }

    // fallback 到原方法
    return this.getMaterialDetailInfo(
      description.materialId,
      description.materialVersion,
    )
  }

  /**
   * 获取物料基础信息，包含错误处理和 fallback 逻辑
   */
  async getMaterialDetailInfo(
    materialId: number,
    materialVersion: string,
  ): Promise<Service.MaterialSmartDescription.MaterialDetailInfo> {
    try {
      const detail
        = await this.materialPlatformService.getMaterialVersionDetail({
          materialId,
          materialVersionName: materialVersion,
        })
      return {
        title: detail.title,
        name: detail.currentVersion.schema.componentName,
        namespace: detail.namespace,
      }
    }
    catch (error) {
      this.logger.error(`获取物料详情失败: ${materialId}`, error)
      return {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }
  }

  /**
   * 富化原始描述数据
   */
  private async enrichRawDescription(
    rawDescription: Service.MaterialSmartDescription.RawDescriptionQueryResult,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    const descriptionObj = {
      id: rawDescription.id,
      materialId: rawDescription.material_id,
      materialVersion: rawDescription.material_version,
      materialPubId: rawDescription.material_pub_id,
      jobId: rawDescription.job_id,
      smartDescription: rawDescription.smart_description,
      createTime: rawDescription.create_time,
      state: rawDescription.state,
      namespace: rawDescription.namespace,
      schemaUrl: rawDescription.schema_url,
      publishStatus: rawDescription.publish_status,
    } as MaterialSmartDescription

    const materialDetail = await this.getMaterialDetailInfoFromDescription(
      descriptionObj,
    )
    const zhidaDescription = this.buildZhidaDescription({
      smartDescription: rawDescription.smart_description,
      materialDetail,
    })

    return {
      id: rawDescription.id,
      materialId: rawDescription.material_id,
      materialVersion: rawDescription.material_version,
      materialPubId: rawDescription.material_pub_id,
      jobId: rawDescription.job_id,
      smartDescription: rawDescription.smart_description,
      createTime: rawDescription.create_time,
      state: rawDescription.state,
      materialDetail,
      zhidaDescription,
      namespace: rawDescription.namespace,
      schemaUrl: rawDescription.schema_url,
      publishStatus: rawDescription.publish_status,
    }
  }
}
