import { Injectable, Logger } from '@nestjs/common'
import { Op } from '@infra-node/sequelize'

import { DatabaseService } from '../../../database/database.service'
import { MaterialSmartDescription } from '../../../database/models'

/**
 * 物料智能描述查询服务
 * 专门负责各种查询操作，遵循单一职责原则
 */
@Injectable()
export class MaterialSmartDescriptionQueryService {
  private readonly logger = new Logger(
    MaterialSmartDescriptionQueryService.name,
  )

  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 根据物料 ID 查找描述
   */
  async findDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料 ID 和版本查找最新的有效描述
   */
  async findLatestDescriptionByMaterial(
    materialId: number,
    materialVersion?: string,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        materialId,
        state: 1, // 只查询正常状态的记录
      }

      if (materialVersion) {
        whereCondition.materialVersion = materialVersion
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`查找最新描述失败 materialId: ${materialId}`, error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的有效描述
   */
  async findLatestDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (params.materialId) {
        whereCondition.materialId = params.materialId
      }

      if (params.materialVersionName) {
        whereCondition.materialVersion = params.materialVersionName
      }

      // 如果提供了 namespace，可以直接通过 namespace 查询
      if (params.namespace) {
        whereCondition.namespace = params.namespace
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('根据物料标识参数查找描述失败', error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的有效且已发布的描述
   * 包含 state = 1 和 publishStatus = 1 的检查
   */
  async findLatestPublishedDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
        publishStatus: 1, // 只查询已发布的记录
      }

      // 根据传入的参数构建查询条件
      if (params.materialId) {
        whereCondition.materialId = params.materialId
      }

      if (params.materialVersionName) {
        whereCondition.materialVersion = params.materialVersionName
      }

      // 如果提供了 namespace，可以直接通过 namespace 查询
      if (params.namespace) {
        whereCondition.namespace = params.namespace
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('根据物料标识参数查找已发布描述失败', error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找所有有效且已发布的描述
   * 用于版本比较和回退查找
   */
  async findAllPublishedDescriptionsByMaterialId(
    materialId: number,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
          publishStatus: 1, // 只查询已发布的记录
        },
        order: [['createTime', 'DESC']],
      })

      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找所有已发布描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 namespace 查找描述（支持模糊查询）
   */
  async findDescriptionsByNamespace(
    namespace: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 模糊查找描述失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 ID 查找单个描述记录
   */
  async findDescriptionById(
    id: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findOne({
        where: {
          id,
          state: 1, // 只查询正常状态的记录
        },
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`根据ID查找描述失败 id: ${id}`, error)
      throw error
    }
  }

  /**
   * 分页查询描述列表
   */
  async findDescriptionsWithPagination(
    params: Service.PaginationParams & {
      materialId?: number
      namespace?: string
      publishStatus?: number
    },
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    try {
      const models = this.databaseService.getModels()
      const { pageNum, pageSize, materialId, namespace, publishStatus }
        = params

      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      if (materialId) {
        whereCondition.materialId = materialId
      }

      if (namespace) {
        whereCondition.namespace = {
          [Op.like]: `%${namespace}%`,
        }
      }

      if (publishStatus !== undefined) {
        whereCondition.publishStatus = publishStatus
      }

      const offset = (pageNum - 1) * pageSize
      const { count, rows }
        = await models.MaterialSmartDescription.findAndCountAll({
          where: whereCondition,
          limit: pageSize,
          offset,
          order: [['createTime', 'DESC']],
        })

      const list = rows.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
      const totalPage = Math.ceil(count / pageSize)

      return {
        list,
        total: count,
        pageNum,
        pageSize,
        totalPage,
        hasNext: pageNum < totalPage,
        hasPrev: pageNum > 1,
      }
    }
    catch (error) {
      this.logger.error('分页查询描述列表失败', error)
      throw error
    }
  }

  /**
   * 获取原始的富化描述数据（用于主服务进一步处理）
   */
  async getEnrichedDescriptionsRaw(
    params: Service.PaginationParams,
  ): Promise<
      Service.PaginationResult<Service.MaterialSmartDescription.RawDescriptionQueryResult>
    > {
    const { pageNum = 1, pageSize = 10 } = params
    const offset = (pageNum - 1) * pageSize

    // 使用兼容 MySQL 5.7 的查询方式，通过子查询获取每个 material_pub_id 的最新记录
    const sql = `
      SELECT d1.*
      FROM material_smart_description d1
      INNER JOIN (
        SELECT material_pub_id, MAX(create_time) as max_create_time
        FROM material_smart_description
        WHERE state = 1
        GROUP BY material_pub_id
      ) d2 ON d1.material_pub_id = d2.material_pub_id AND d1.create_time = d2.max_create_time
      WHERE d1.state = 1
      ORDER BY d1.create_time DESC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    const countSql = `
      SELECT COUNT(DISTINCT material_pub_id) as total
      FROM material_smart_description
      WHERE state = 1
    `

    try {
      const [results, countResults] = await Promise.all([
        this.databaseService.query(sql),
        this.databaseService.query(countSql),
      ])

      const total
        = ((countResults as unknown[])[0] as { total: number })?.total || 0
      const totalPage = Math.ceil(total / pageSize)

      return {
        list: results as Service.MaterialSmartDescription.RawDescriptionQueryResult[],
        total,
        pageNum,
        pageSize,
        totalPage,
        hasNext: pageNum < totalPage,
        hasPrev: pageNum > 1,
      }
    }
    catch (error) {
      this.logger.error('获取富化描述列表失败', error)
      throw error
    }
  }

  /**
   * 根据命名空间统计描述数量
   */
  async countDescriptionsByNamespace(namespace: string): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`,
          },
          state: 1,
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据命名空间统计描述数量失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态查找描述
   */
  async findDescriptionsByPublishStatus(
    publishStatus: number,
    limit: number,
    offset: number,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          publishStatus,
          state: 1,
        },
        limit,
        offset,
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据发布状态查找描述失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态统计描述数量
   */
  async countDescriptionsByPublishStatus(
    publishStatus: number,
  ): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          publishStatus,
          state: 1,
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据发布状态统计描述数量失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }
}
