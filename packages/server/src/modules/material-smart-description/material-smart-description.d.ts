declare namespace Service.MaterialSmartDescription {
  // ==================== 类型定义 ====================

  /**
   * 发布状态类型
   */
  export type PublishStatus = 0 | 1 // 0: 草稿, 1: 正式发布

  /**
   * 记录状态类型
   */
  export type RecordState = -1 | 0 | 1 // -1: 删除, 0: 待上线, 1: 正常

  /**
   * 查询限制类型
   */
  export type QueryLimits = {
    readonly MAX_PAGE_SIZE: 100
    readonly DEFAULT_PAGE_SIZE: 10
    readonly MAX_NAMESPACE_LENGTH: 100
  }
  export interface BasicMaterialDescription {
    exportType: {
      type:
        | 'Component'
        | 'Function'
        | 'Class'
        | 'Hook'
        | 'Type'
        | 'Other'
        | (string & {})
      explanation: string
    }

    functionality: {
      features: string[]
    }

    scenarios: {
      cases: string[]
    }

    uiStructure: {
      styleFeatures: string[]
    }

    usage: {
      basicExample: string[]
    }

    api: {
      parameters: {
        typescriptCode: string
      }

      returnValue: {
        type: string
        description: string
      }
    }

    considerations: {
      limitations: string[]
      performance: string[]
    }

    bestPractices: {
      recommendations: string[]
      antiPatterns: string[]
    }

    decisiveFactors: {
      advantages: string[]
      limitations: string[]
    }

    payload?: Partial<MaterialUserDefine>
  }

  export interface ZhiDaNeededMaterialDescription
    extends Partial<MaterialUserDefine> {
    namespace: string
    title: string // 中文名称
    name: string // 英文名称（组件名称）
    description: string // from BasicMaterialDescription.exportType.explanation
    propsDefine: string // from BasicMaterialDescription.api.parameters.typescriptCode
    jsxDemo: string[] // from BasicMaterialDescription.usage.basicExample
  }

  /**
   * 带版本信息的 ZhiDa 描述
   * 用于新的查询接口，包含版本匹配信息
   */
  export interface ZhiDaNeededMaterialDescriptionWithVersion {
    content: ZhiDaNeededMaterialDescription
    isTargetVersion: boolean // 是否是目标版本的描述
    actualVersion: string // 实际返回的版本号
    targetVersion: string // 目标版本号
  }

  export interface MaterialUserDefine {
    /**
     * 使用当前组件必须附带使用的组件，比如 Drawer 组件的 content 需要嵌套 DrawerContent 组件
     * @example ['@es/tianhe-basic-materials::DrawerContent', '@es/tianhe-basic-materials::DrawerFooterContent']
     **/
    childNested?: string[]
    /**
     * 支持兼容物料生成数据层级错误，比如 Form 的内容在 items 中，但大模型可能返回内容在 children 内
     * @example { children: 'drawerContent' }
     **/
    jsxPropCompatible?: Record<string, unknown>
    /**
     * 支持物料 prop 二次适配，比如 Card 组件判断 title 属性不存在时，将其 headStyle 需要设置为 { display: none }
     * @example
     * // Card 组件，当 title 不存在时
     * // 设置 headStyle={ type: 'static', value: display: 'none' } },
     *
     * [{
     *   // 断言，当 title 属性不存在时
     *   predicate: props => !props.title,
     *   alias: {
     *     // 对应设置的属性名
     *     key: 'headStyle',
     *     // 对应设置的属性值
     *     value: {
     *       type: 'object',
     *       value: {
     *         display: 'none',
     *       },
     *     },
     *   },
     * }]
     *
     * // 示例: Card props
     * {
     *   bordered: false,
     *   loading: false
     * }
     *
     * // 识别 effect 转换后的 props
     * {
     *   headStyle: {
     *     type: 'static',
     *     value: {
     *       display: 'none'
     *     }
     *   }
     * }
     **/
    purePropEffect?: Array<{
      predicate: string
      alias: {
        key: string
        value: unknown
      }
    }>
    /**
     * 在插入组件之前需要合并 prop
     * @example // 声明在插入组件前需要合并的 props
     * {
     *    subTitle: '',
     *    subTitleIcon: '',
     *    height: 'auto',
     * }
     **/
    mergePropsBeforeInsert?: Record<string, unknown>
  }

  /**
   * 物料基础信息
   */
  export interface MaterialDetailInfo {
    title: string
    name: string
    namespace: string
  }

  /**
   * 更新 ZhiDa 描述的 DTO
   * 只允许修改映射到 zhidaDescription 的字段，除了 name 和 namespace
   */
  export interface UpdateZhidaDescriptionDto
    extends Partial<ZhiDaNeededMaterialDescription> {
    description?: string // 对应 smartDescription.exportType.explanation
    propsDefine?: string // 对应 smartDescription.api.parameters.typescriptCode
    jsxDemo?: string[] // 对应 smartDescription.usage.basicExample
  }

  /**
   * 按物料分组的描述数据
   */
  export interface MaterialGroupedDescription {
    materialId: number
    materialDetail: MaterialDetailInfo
    totalVersions: number
    latestVersion: string
    versions: EnrichedDescription[]
  }

  /**
   * 按物料分组的描述列表结果
   */
  export interface MaterialGroupedDescriptionList {
    list: MaterialGroupedDescription[]
    total: number
    pageNum: number
    pageSize: number
    totalPage: number
    hasNext: boolean
    hasPrev: boolean
  }

  /**
   * 富化后的描述信息，包含物料详情和 zhidaDescription
   */
  export interface EnrichedDescription {
    id: number
    materialId: number
    materialVersion: string
    materialPubId: number
    jobId: number
    smartDescription: import('type-fest').JsonValue
    zhidaDescription: ZhiDaNeededMaterialDescription
    createTime: number
    state: number
    materialDetail: MaterialDetailInfo
    namespace: string
    schemaUrl?: string | null
    publishStatus: number
  }

  /**
   * 原始数据库查询结果
   */
  export interface RawDescriptionQueryResult {
    id: number
    material_id: number
    material_version: string
    material_pub_id: number
    job_id: number
    smart_description: import('type-fest').JsonValue
    create_time: number
    state: number
    namespace: string
    schema_url?: string | null
    publish_status: number
  }

  /**
   * ZhiDa 描述更新参数
   */
  export interface UpdateZhidaDescriptionParams {
    id: number
    updateDto: UpdateZhidaDescriptionDto
  }

  /**
   * ZhiDa 描述更新结果
   */
  export interface UpdateZhidaDescriptionResult {
    success: boolean
    data?: EnrichedDescription
    error?: string
  }

  /**
   * 构建 ZhiDa 描述的参数
   */
  export interface BuildZhidaDescriptionParams {
    smartDescription: import('type-fest').JsonValue
    materialDetail: MaterialDetailInfo
  }

  /**
   * 基于现有记录创建新记录的参数
   */
  export interface CreateSmartDescriptionFromExistingParams {
    originalDescription: import('@/database/models').MaterialSmartDescription
    updatedSmartDescription: Service.MaterialSmartDescription.Job.BasicMaterialDescription
  }
}
